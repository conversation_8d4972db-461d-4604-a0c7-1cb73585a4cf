package br.com.estudoorganizado.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class ChatClientConfig {

    @Bean
    @ConditionalOnBean(ChatModel.class)
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        // Log para debug
        System.out.println("Criando ChatClient.Builder com modelo: " + chatModel.getClass().getSimpleName());

        return ChatClient.builder(chatModel);
    }
}
