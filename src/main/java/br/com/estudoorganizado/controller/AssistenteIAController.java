package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.model.ConversaAssistenteIA;
import br.com.estudoorganizado.model.MensagemConversa;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.ConversaAssistenteAiService;
import br.com.estudoorganizado.util.Util;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v1/assistente-ia")
@Tag(name = "Assistente", description = "Assistente de IA conversacional")
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class AssistenteIAController {

    private final ChatClient chatClient;
    private final ConversaAssistenteAiService conversaAssistenteIAService;

    public AssistenteIAController(ChatClient.Builder chatClientBuilder,
                                 ConversaAssistenteAiService conversaAssistenteIAService) {

        // Configurar ChatClient com as funções disponíveis
        this.chatClient = chatClientBuilder
            .defaultSystem("Você é um assistente especializado em planejamento de estudos para concursos públicos. " +
                "Você pode ajudar os usuários a cadastrar disciplinas, criar e gerenciar planejamentos de estudo. " +
                "Seja sempre educado, claro e objetivo em suas respostas. " +
                "Quando o usuário solicitar cadastrar uma disciplina, use a função 'cadastrarDisciplina' disponível. " +
                "Para cadastrar uma disciplina, você precisa apenas do nome da disciplina. " +
                "Sempre pergunte ao usuário qual o nome da disciplina que ele deseja cadastrar se não foi informado.")
            .build();

        this.conversaAssistenteIAService = conversaAssistenteIAService;

        log.info("ChatClient configurado para assistente de IA");
    }

    @PostMapping("/chat")
    @Operation(summary = "Chat com assistente de IA com histórico",
               description = "Envia uma mensagem para o assistente de IA mantendo o contexto da conversa")
    public ResponseEntity<ChatResponseDTO> chat(@Valid @RequestBody ChatRequestDTO request,
                                                @AuthenticationPrincipal User user) {
        try {
            log.info("Recebida mensagem para o assistente do usuário: {}", user.getEmail());

            // Buscar ou criar conversa
            ConversaAssistenteIA conversaAssistenteIA = obterOuCriarConversa(request, user);

            // Salvar mensagem do usuário
            conversaAssistenteIAService.adicionarMensagem(conversaAssistenteIA, request.getMensagem(), MensagemConversa.TipoMensagem.USER, user);

            // Buscar histórico para contexto
            List<MensagemConversaDTO> historicoRecente = conversaAssistenteIAService.buscarUltimasMensagens(conversaAssistenteIA, 10);

            // Construir contexto da conversa
            List<Message> mensagensContexto = construirContextoConversa(historicoRecente);

            // Gerar resposta com contexto e funções
            String resposta = chatClient.prompt()
                .messages(mensagensContexto)
                .functions("cadastrarDisciplina", "salvarOuAtualizarPlanejamento", "criarPlanejamentoComIA")
                .call()
                .content();

            // Salvar resposta do assistente
            conversaAssistenteIAService.adicionarMensagem(conversaAssistenteIA, resposta, MensagemConversa.TipoMensagem.ASSISTANT, user);

            log.info("Resposta gerada pelo assistente para conversa: {}", conversaAssistenteIA.getId());

            return ResponseEntity.ok(ChatResponseDTO.builder()
                .resposta(resposta)
                .conversaId(conversaAssistenteIA.getId())
                .tituloConversa(conversaAssistenteIA.getTitulo())
                .novaConversa(request.getConversaId() == null)
                .build());

        } catch (Exception e) {
            log.error("Erro ao processar mensagem do assistente: {}", e.getMessage(), e);
            return ResponseEntity.ok(ChatResponseDTO.builder()
                .resposta("Desculpe, ocorreu um erro ao processar sua solicitação. Tente novamente.")
                .build());
        }
    }


    private ConversaAssistenteIA obterOuCriarConversa(ChatRequestDTO request, User user) {
        if (!Util.emptyNumber(request.getConversaId())) {
            return conversaAssistenteIAService.buscarConversaPorId(request.getConversaId(), user)
                .orElseThrow(() -> new RuntimeException("Conversa não encontrada"));
        } else {
            String titulo = !Util.emptyString(request.getTituloConversa()) ?
                          request.getTituloConversa() :
                          conversaAssistenteIAService.gerarTituloAutomatico(request.getMensagem());
            return conversaAssistenteIAService.criarNovaConversa(user, titulo);
        }
    }

    private List<Message> construirContextoConversa(List<MensagemConversaDTO> historico) {
        List<Message> mensagens = new ArrayList<>();

        for (MensagemConversaDTO msg : historico) {
            if (msg.getTipo() == MensagemConversa.TipoMensagem.USER) {
                mensagens.add(new UserMessage(msg.getConteudo()));
            } else {
                mensagens.add(new AssistantMessage(msg.getConteudo()));
            }
        }

        return mensagens;
    }

    @GetMapping("/conversas")
    @Operation(summary = "Listar conversas do usuário",
               description = "Retorna todas as conversas do usuário ordenadas pela data da última mensagem")
    public ResponseEntity<List<ConversaAssistenteDTO>> listarConversas(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<ConversaAssistenteDTO> conversas = conversaAssistenteIAService.listarConversasDoUsuario(user);
            return ResponseEntity.ok(conversas);
        } catch (Exception e) {
            log.error("Erro ao listar conversas: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/conversas/{conversaId}")
    @Operation(summary = "Obter histórico de uma conversa",
               description = "Retorna o histórico completo de mensagens de uma conversa específica")
    public ResponseEntity<ConversaAssistenteDTO> obterHistoricoConversa(@PathVariable Long conversaId,
                                                                       Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            ConversaAssistenteDTO conversa = conversaAssistenteIAService.buscarHistoricoCompleto(conversaId, user);

            if (conversa == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(conversa);
        } catch (Exception e) {
            log.error("Erro ao obter histórico da conversa {}: {}", conversaId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/conversas")
    @Operation(summary = "Criar nova conversa",
               description = "Cria uma nova conversa vazia para o usuário")
    public ResponseEntity<ConversaAssistenteDTO> criarNovaConversa(@RequestBody(required = false) String titulo,
                                                                  Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String tituloConversa = titulo != null && !titulo.trim().isEmpty() ? titulo.trim() : "Nova Conversa";

            ConversaAssistenteIA conversa = conversaAssistenteIAService.criarNovaConversa(user, tituloConversa);

            ConversaAssistenteDTO conversaDTO = ConversaAssistenteDTO.builder()
                .id(conversa.getId())
                .titulo(conversa.getTitulo())
                .dataCriacao(conversa.getDataCriacao())
                .dataUltimaMensagem(conversa.getDataUltimaMensagem())
                .totalMensagens(0)
                .build();

            return ResponseEntity.ok(conversaDTO);
        } catch (Exception e) {
            log.error("Erro ao criar nova conversa: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/conversas/{conversaId}/titulo")
    @Operation(summary = "Atualizar título da conversa",
               description = "Atualiza o título de uma conversa específica")
    public ResponseEntity<Void> atualizarTituloConversa(@PathVariable Long conversaId,
                                                        @RequestBody String novoTitulo,
                                                        Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            ConversaAssistenteIA conversa = conversaAssistenteIAService.buscarConversaPorId(conversaId, user)
                .orElse(null);

            if (conversa == null) {
                return ResponseEntity.notFound().build();
            }

            conversaAssistenteIAService.atualizarTitulo(conversa, novoTitulo.trim());
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Erro ao atualizar título da conversa {}: {}", conversaId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @DeleteMapping("/conversas/{conversaId}")
    @Operation(summary = "Remover conversa",
               description = "Remove uma conversa e todo seu histórico de mensagens")
    public ResponseEntity<Void> removerConversa(@PathVariable Long conversaId,
                                               Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            conversaAssistenteIAService.removerConversa(conversaId, user);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Erro ao remover conversa {}: {}", conversaId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
