# Guia: Chat Cliente com Cadastro de Disciplinas

## Visão Geral

O sistema agora possui integração entre o chat cliente (AssistenteIAController) e as funções de cadastro de disciplinas da classe `FuncoesIAConfig`. Isso permite que o usuário converse naturalmente com o assistente de IA e solicite o cadastro de disciplinas.

## Como Funciona

### 1. Configuração Técnica

O `AssistenteIAController` foi modificado para:
- Carregar automaticamente as funções disponíveis do Spring Context
- Configurar o ChatClient com as funções de IA
- Incluir instruções específicas sobre cadastro de disciplinas no prompt do sistema

### 2. Funções Disponíveis

#### Cadastrar Disciplina
- **Função**: `cadastrarDisciplina`
- **Parâmetro**: Nome da disciplina (String)
- **Descrição**: Cadastra uma nova disciplina no sistema para o usuário autenticado
- **Retorno**: Mensagem de sucesso com ID da disciplina ou erro

#### Outras Funções (se disponíveis)
- `salvarOuAtualizarPlanejamento`: Para criar/atualizar planejamentos
- `criarPlanejamentoComIA`: Para gerar planejamentos com IA

## Como Usar

### 1. Endpoint do Chat
```
POST /v1/assistente-ia/chat
```

### 2. Exemplos de Conversas

#### Exemplo 1: Cadastro Direto
**Usuário**: "Quero cadastrar a disciplina Matemática"

**Assistente**: A IA automaticamente identificará a intenção e usará a função `cadastrarDisciplina` com o parâmetro "Matemática", retornando algo como:
"Disciplina 'Matemática' foi cadastrada com sucesso com o ID 123."

#### Exemplo 2: Solicitação sem Especificar Nome
**Usuário**: "Preciso cadastrar uma nova disciplina"

**Assistente**: "Para cadastrar uma disciplina, preciso saber o nome dela. Qual o nome da disciplina que você deseja cadastrar?"

**Usuário**: "Português"

**Assistente**: Usará a função para cadastrar "Português" e retornará a confirmação.

#### Exemplo 3: Múltiplas Disciplinas
**Usuário**: "Quero cadastrar Direito Constitucional e Direito Administrativo"

**Assistente**: Pode processar ambas as disciplinas, cadastrando uma por vez.

### 3. Estrutura da Requisição
```json
{
  "mensagem": "Quero cadastrar a disciplina Matemática",
  "conversaId": null,
  "tituloConversa": "Cadastro de Disciplinas"
}
```

### 4. Estrutura da Resposta
```json
{
  "resposta": "Disciplina 'Matemática' foi cadastrada com sucesso com o ID 123.",
  "conversaId": 456,
  "tituloConversa": "Cadastro de Disciplinas",
  "novaConversa": true
}
```

## Vantagens da Implementação

### 1. Interface Natural
- O usuário não precisa conhecer APIs específicas
- Conversa em linguagem natural
- Contexto mantido durante a conversa

### 2. Validação Automática
- A função valida automaticamente os dados
- Retorna erros claros em caso de problemas
- Associa automaticamente ao usuário autenticado

### 3. Histórico de Conversas
- Todas as interações são salvas
- Possível revisar cadastros anteriores
- Contexto mantido entre mensagens

### 4. Extensibilidade
- Fácil adicionar novas funções
- Sistema modular e configurável
- Reutilização das regras de negócio existentes

## Configuração Necessária

### 1. Propriedades
Certifique-se que está habilitado no `application.properties`:
```properties
utilizar-spring-ai=true
```

### 2. Autenticação
O usuário deve estar autenticado para usar o chat, pois as disciplinas são associadas ao usuário logado.

### 3. Dependências Spring AI
As dependências do Spring AI devem estar configuradas corretamente para o Vertex AI Gemini.

## Testando a Funcionalidade

### 1. Teste Manual via Swagger/Postman
1. Faça login para obter o token JWT
2. Use o endpoint `/v1/assistente-ia/chat`
3. Envie mensagens solicitando cadastro de disciplinas
4. Verifique se as disciplinas foram criadas via `/v1/disciplinas`

### 2. Exemplos de Mensagens para Teste
- "Cadastre a disciplina História"
- "Preciso adicionar Física no meu sistema"
- "Quero criar uma nova matéria chamada Química"
- "Adicione Biologia às minhas disciplinas"

### 3. Verificação
Após cada cadastro, você pode verificar se a disciplina foi criada consultando:
```
GET /v1/disciplinas
```

## Próximos Passos

1. **Testes**: Implemente testes automatizados para validar a integração
2. **Mais Funções**: Adicione outras funcionalidades como listar, editar e excluir disciplinas
3. **Melhorias na IA**: Refine os prompts para melhor compreensão das intenções
4. **Interface Frontend**: Crie uma interface de chat amigável para os usuários
